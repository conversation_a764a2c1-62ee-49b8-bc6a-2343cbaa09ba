// HKEX Leadership Interactive Script
// Handles filtering, searching, and display functionality

let currentData = [];
let currentSection = 'board';

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    loadData();
    setupEventListeners();
    showSection('board');
});

function loadData() {
    currentData = hkexLeadershipData.boardMembers;
    renderLeadership();
    updateStats();
}

function setupEventListeners() {
    document.getElementById('roleFilter').addEventListener('change', applyFilters);
    document.getElementById('committeeFilter').addEventListener('change', applyFilters);
    document.getElementById('searchFilter').addEventListener('input', applyFilters);
}

function showSection(section) {
    // Update active tab
    document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
    event.target.classList.add('active');
    
    // Show/hide sections
    document.querySelectorAll('.section').forEach(sec => sec.classList.remove('active'));
    document.getElementById(section).classList.add('active');
    
    currentSection = section;
    
    // Load appropriate data
    if (section === 'board') {
        currentData = hkexLeadershipData.boardMembers;
        renderLeadership();
    } else if (section === 'management') {
        currentData = hkexLeadershipData.managementCommittee;
        renderLeadership();
    }
}

function applyFilters() {
    const roleFilter = document.getElementById('roleFilter').value;
    const committeeFilter = document.getElementById('committeeFilter').value;
    const searchFilter = document.getElementById('searchFilter').value.toLowerCase();
    
    let filteredData = currentSection === 'board' ? 
        hkexLeadershipData.boardMembers : 
        hkexLeadershipData.managementCommittee;
    
    // Apply role filter
    if (roleFilter) {
        filteredData = filteredData.filter(person => person.role === roleFilter);
    }
    
    // Apply committee filter
    if (committeeFilter) {
        filteredData = filteredData.filter(person => 
            person.committees && person.committees.some(committee => 
                committee.toLowerCase().includes(committeeFilter)
            )
        );
    }
    
    // Apply search filter
    if (searchFilter) {
        filteredData = filteredData.filter(person => 
            person.name.toLowerCase().includes(searchFilter) ||
            person.title.toLowerCase().includes(searchFilter) ||
            (person.background && person.background.toLowerCase().includes(searchFilter))
        );
    }
    
    currentData = filteredData;
    renderLeadership();
}

function renderLeadership() {
    const gridId = currentSection === 'board' ? 'boardGrid' : 'managementGrid';
    const grid = document.getElementById(gridId);
    
    if (!grid) return;
    
    grid.innerHTML = '';
    
    currentData.forEach(person => {
        const card = createPersonCard(person);
        grid.appendChild(card);
    });
}

function createPersonCard(person) {
    const card = document.createElement('div');
    card.className = 'person-card';
    
    const committeesHtml = person.committees ? 
        person.committees.map(committee => 
            `<span class="committee-tag">${committee}</span>`
        ).join('') : '';
    
    const qualificationsHtml = person.qualifications ? 
        person.qualifications.map(qual => 
            `<div class="detail-item"><i class="fas fa-graduation-cap"></i> ${qual}</div>`
        ).join('') : '';
    
    const achievementsHtml = person.achievements ? 
        person.achievements.map(achievement => 
            `<div class="detail-item"><i class="fas fa-trophy"></i> ${achievement}</div>`
        ).join('') : '';
    
    card.innerHTML = `
        <div class="person-header">
            <div class="person-avatar">${person.avatar}</div>
            <div class="person-info">
                <h3>${person.name}</h3>
                <div class="person-title">${person.title}</div>
            </div>
        </div>
        
        <div class="person-details">
            ${person.age ? `<div class="detail-item"><i class="fas fa-calendar"></i> Age: ${person.age}</div>` : ''}
            ${person.joinedDate ? `<div class="detail-item"><i class="fas fa-calendar-plus"></i> Joined: ${person.joinedDate}</div>` : ''}
            ${person.ceoSince ? `<div class="detail-item"><i class="fas fa-crown"></i> CEO Since: ${person.ceoSince}</div>` : ''}
            ${person.termOfOffice ? `<div class="detail-item"><i class="fas fa-clock"></i> Term: ${person.termOfOffice}</div>` : ''}
            ${person.department ? `<div class="detail-item"><i class="fas fa-building"></i> Department: ${person.department}</div>` : ''}
            ${person.type ? `<div class="detail-item"><i class="fas fa-user-tag"></i> ${person.type}</div>` : ''}
            
            ${qualificationsHtml}
            ${achievementsHtml}
            
            <div class="detail-item">
                <i class="fas fa-info-circle"></i> 
                <span>${person.background}</span>
            </div>
            
            ${person.committees ? `
                <div class="committees">
                    <div style="margin-bottom: 8px; font-weight: 600; color: #667eea;">
                        <i class="fas fa-users"></i> Committees:
                    </div>
                    ${committeesHtml}
                </div>
            ` : ''}
        </div>
    `;
    
    return card;
}

function updateStats() {
    const boardMembers = hkexLeadershipData.boardMembers.length;
    const managementMembers = hkexLeadershipData.managementCommittee.length;
    const independentDirectors = hkexLeadershipData.boardMembers.filter(member => 
        member.role === 'independent'
    ).length;
    
    document.getElementById('totalBoard').textContent = boardMembers;
    document.getElementById('totalManagement').textContent = managementMembers;
    document.getElementById('independentDirectors').textContent = independentDirectors;
}

// Search functionality
function searchLeadership(query) {
    const searchTerm = query.toLowerCase();
    const allPeople = [...hkexLeadershipData.boardMembers, ...hkexLeadershipData.managementCommittee];
    
    return allPeople.filter(person => 
        person.name.toLowerCase().includes(searchTerm) ||
        person.title.toLowerCase().includes(searchTerm) ||
        (person.background && person.background.toLowerCase().includes(searchTerm)) ||
        (person.committees && person.committees.some(committee => 
            committee.toLowerCase().includes(searchTerm)
        ))
    );
}

// Export functions for external use
window.hkexLeadership = {
    showSection,
    searchLeadership,
    applyFilters,
    data: hkexLeadershipData
};
